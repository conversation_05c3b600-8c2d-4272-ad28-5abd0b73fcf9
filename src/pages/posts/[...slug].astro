---
import { render } from "astro:content";
import { getAllPosts } from "@/data/post";
import PostLayout from "@/layouts/BlogPost.astro";
import type { GetStaticPaths, InferGetStaticPropsType } from "astro";

// if you're using an adaptor in SSR mode, getStaticPaths wont work -> https://docs.astro.build/en/guides/routing/#modifying-the-slug-example-for-ssr
export const getStaticPaths = (async () => {
	const blogEntries = await getAllPosts();
	return blogEntries.map((post) => ({
		params: { slug: post.id },
		props: { post },
	}));
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { post } = Astro.props;
const { Content } = await render(post);
---

<PostLayout post={post}>
	<Content />
</PostLayout>
