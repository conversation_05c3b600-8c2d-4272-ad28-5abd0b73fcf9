/* would like to ignore ./src/pages/og-image/[slug].png.ts */
@import "tailwindcss";
/* config for tailwindcss-typography plugin */
@config "../../tailwind.config.ts";

/* use a selector-based strategy for dark mode */
@variant dark (&:where([data-theme="dark"], [data-theme="dark"] *));

/* 基于 #00769B 主题色调整的色彩配置 */
@theme {
	--color-global-bg: oklch(98.48% 0 0);
	--color-global-text: oklch(26.99% 0.0096 235.05);
	--color-link: oklch(45% 0.12 205); /* 基于 #00769B 调整的链接色 */
	--color-accent: oklch(45% 0.12 205); /* #00769B 对应的 OKLCH 值 */
	--color-accent-2: oklch(20% 0.02 205); /* 基于主题色的深色强调 */
	--color-quote: oklch(50% 0.1 205); /* 基于主题色调整的引用色 */
}

@layer base {
	html {
		color-scheme: light dark;
		accent-color: var(--color-accent);
		scrollbar-gutter: stable;

		&[data-theme="light"] {
			color-scheme: light;
		}

		&[data-theme="dark"] {
			color-scheme: dark;
			--color-global-bg: oklch(23.64% 0.0045 248);
			--color-global-text: oklch(83.54% 0 264);
			--color-link: oklch(65% 0.15 205); /* 深色模式下基于主题色的链接色 */
			--color-accent: oklch(60% 0.15 205); /* 深色模式下的主题色 */
			--color-accent-2: oklch(90% 0.02 205); /* 深色模式下的浅色强调 */
			--color-quote: oklch(70% 0.12 205); /* 深色模式下的引用色 */
		}
	}

	:target {
		scroll-margin-block: 5ex;
	}

	@view-transition {
		navigation: auto;
	}

	/* Astro image responsive styles, modified from -> https://docs.astro.build/en/guides/images/#responsive-image-styles */
	:where([data-astro-image]) {
		object-fit: var(--fit);
		object-position: var(--pos);
	}
	[data-astro-image="full-width"] {
		width: 100%;
	}
	[data-astro-image="constrained"] {
		max-width: 100%;
	}
}

@layer components {
	@import "./components/admonition.css";
	@import "./components/github-card.css";

	.cactus-link {
		@apply hover:decoration-link underline underline-offset-3 hover:decoration-2;
	}

	.title {
		@apply text-accent-2 text-2xl font-semibold;
	}
}

@utility prose {
	--tw-prose-body: var(--color-global-text);
	--tw-prose-bold: var(--color-global-text);
	--tw-prose-bullets: var(--color-global-text);
	--tw-prose-code: var(--color-global-text);
	--tw-prose-headings: var(--color-accent-2);
	--tw-prose-hr: 0.5px dashed #666;
	--tw-prose-links: var(--color-global-text);
	--tw-prose-quotes: var(--color-quote);
	--tw-prose-th-borders: #666;
}
