@import "@pagefind/default-ui/css/ui.css";

:root {
	--pagefind-ui-font: inherit;
}

#cactus__search {
	--pagefind-ui-primary: var(--color-accent);
	--pagefind-ui-text: var(--color-global-text);
	--pagefind-ui-background: var(--color-zinc-100);
	--pagefind-ui-border: var(--color-zinc-300);
	--pagefind-ui-border-width: 1px;

	[data-theme="dark"] & {
		--pagefind-ui-background: var(--color-zinc-800);
		--pagefind-ui-border: var(--color-zinc-500);
	}
}

#cactus__search .pagefind-ui__search-clear {
	width: calc(60px * var(--pagefind-ui-scale));
	padding: 0;
	background-color: transparent;
	overflow: hidden;
}

#cactus__search .pagefind-ui__search-clear:focus {
	outline: 1px solid var(--color-accent-2);
}

#cactus__search .pagefind-ui__search-clear::before {
	content: "";
	-webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' %3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'%3E%3C/path%3E%3C/svg%3E")
		center / 60% no-repeat;
	mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' %3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'%3E%3C/path%3E%3C/svg%3E")
		center / 60% no-repeat;
	background-color: var(--color-accent);
	display: block;
	width: 100%;
	height: 100%;
}

#cactus__search .pagefind-ui__result {
	border: 0;
}

#cactus__search .pagefind-ui__result-link {
	background-size: 100% 6px;
	background-position: bottom;
	background-repeat: repeat-x;
	background-image: linear-gradient(
		transparent,
		transparent 5px,
		var(--color-global-text) 5px,
		var(--color-global-text)
	);
}

#cactus__search .pagefind-ui__result-link:hover {
	text-decoration: none;
	background-image: linear-gradient(
		transparent,
		transparent 4px,
		var(--color-link) 4px,
		var(--color-link)
	);
}

#cactus__search mark {
	color: var(--color-quote);
	background-color: transparent;
	font-weight: 600;
}
