{/* Inlined to avoid FOUC. This is a parser blocking script. */}
<script is:inline>
	const lightModePref = window.matchMedia("(prefers-color-scheme: light)");

	function getUserPref() {
		const storedTheme = typeof localStorage !== "undefined" && localStorage.getItem("theme");
		return storedTheme || (lightModePref.matches ? "light" : "dark");
	}

	function setTheme(newTheme) {
		if (newTheme !== "light" && newTheme !== "dark") {
			return console.warn(
				`Invalid theme value '${newTheme}' received. Expected 'light' or 'dark'.`,
			);
		}

		const root = document.documentElement;

		// root already set to newTheme, exit early
		if (newTheme === root.getAttribute("data-theme")) {
			return;
		}

		root.setAttribute("data-theme", newTheme);

		if (typeof localStorage !== "undefined") {
			localStorage.setItem("theme", newTheme);
		}
	}

	// initial setup
	setTheme(getUserPref());

	// View Transitions hook to restore theme
	document.addEventListener("astro:after-swap", () => setTheme(getUserPref()));

	// listen for theme-change custom event, fired in src/components/ThemeToggle.astro
	document.addEventListener("theme-change", (e) => {
		setTheme(e.detail.theme);
	});

	// listen for prefers-color-scheme change.
	lightModePref.addEventListener("change", (e) => setTheme(e.matches ? "light" : "dark"));
</script>
