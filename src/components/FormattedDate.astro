---
import { getFormattedDate } from "@/utils/date";
import type { HTMLAttributes } from "astro/types";

type Props = HTMLAttributes<"time"> & {
	date: Date;
	dateTimeOptions?: Intl.DateTimeFormatOptions;
};

const { date, dateTimeOptions, ...attrs } = Astro.props;

const postDate = getFormattedDate(date, dateTimeOptions);
const ISO = date.toISOString();
---

<time datetime={ISO} title={ISO} {...attrs}>{postDate}</time>
