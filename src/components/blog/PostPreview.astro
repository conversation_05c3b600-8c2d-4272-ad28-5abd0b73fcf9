---
import type { CollectionEntry } from "astro:content";
import FormattedDate from "@/components/FormattedDate.astro";
import type { HTMLTag, Polymorphic } from "astro/types";

type Props<Tag extends HTMLTag> = Polymorphic<{ as: Tag }> & {
	post: CollectionEntry<"post">;
	withDesc?: boolean;
};

const { as: Tag = "div", post, withDesc = false } = Astro.props;
---

<FormattedDate
	class="min-w-30 font-semibold text-gray-600 dark:text-gray-400"
	date={post.data.publishDate}
/>
<Tag>
	{post.data.draft && <span class="text-red-500">(Draft) </span>}
	<a class="cactus-link" href={`/posts/${post.id}/`}>
		{post.data.title}
	</a>
</Tag>
{withDesc && <q class="line-clamp-3 italic">{post.data.description}</q>}
