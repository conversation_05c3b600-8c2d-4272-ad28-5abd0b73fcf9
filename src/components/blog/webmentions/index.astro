---
import { getWebmentionsForUrl } from "@/utils/webmentions";
import Comments from "./Comments.astro";
import Likes from "./Likes.astro";

const url = new URL(Astro.url.pathname, Astro.site);

const webMentions = await getWebmentionsForUrl(`${url}`);

// Return if no webmentions
if (!webMentions.length) return;
---

<hr class="border-solid" />
<h2 class="mb-8 before:hidden">Webmentions for this post</h2>
<div class="space-y-10">
	<Likes mentions={webMentions} />
	<Comments mentions={webMentions} />
</div>
<p class="mt-8">
	Responses powered by{" "}
	<a href="https://webmention.io" rel="noreferrer" target="_blank">Webmentions</a>
</p>
