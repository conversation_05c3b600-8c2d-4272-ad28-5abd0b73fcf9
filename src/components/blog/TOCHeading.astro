---
import type { TocItem } from "@/utils/generateToc";

interface Props {
	heading: TocItem;
}

const {
	heading: { children, depth, slug, text },
} = Astro.props;
---

<li class={`${depth > 2 ? "ms-2" : ""}`}>
	<a
		class={`line-clamp-2 hover:text-accent ${depth <= 2 ? "mt-3" : "mt-2 text-xs"}`}
		href={`#${slug}`}><span aria-hidden="true" class="me-0.5">#</span>{text}</a
	>
	{
		!!children.length && (
			<ol>
				{children.map((subheading) => (
					<Astro.self heading={subheading} />
				))}
			</ol>
		)
	}
</li>
