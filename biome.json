{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "assist": {"actions": {"source": {"organizeImports": "on"}}}, "formatter": {"indentStyle": "tab", "indentWidth": 2, "lineWidth": 100, "formatWithErrors": true, "includes": ["**", "!**/*.astro"]}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "off"}, "suspicious": {"noExplicitAny": "warn"}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"trailingCommas": "all", "semicolons": "always"}}, "vcs": {"clientKind": "git", "enabled": true, "useIgnoreFile": true}, "overrides": [{"includes": ["**/*.astro"], "linter": {"rules": {"correctness": {"noUnusedVariables": "off", "noUnusedImports": "off"}}}}]}